<?php

namespace App;

use Foundation\Lib\Role as RoleConst;
use Illuminate\Contracts\Auth\MustVerifyEmail;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\Relations\MorphMany;
use Illuminate\Database\Eloquent\SoftDeletes;
use Illuminate\Foundation\Auth\User as Authenticatable;
use Illuminate\Notifications\Notifiable;
use Laravel\Passport\HasApiTokens;
use Neputer\Supports\Cache\Cacheable;
use Neputer\Supports\Mixins\HasImage;
use Neputer\Supports\Mixins\HasRoles;

/**
 * Class User
 * @package Foundation\Models
 */
class User extends Authenticatable implements MustVerifyEmail
{
    use HasImage;

    use HasApiTokens, HasRoles, Notifiable, SoftDeletes, Cacheable;

    protected $cacheKey = 'users';

    const DEFAULT_ROLE = 'super-admin';

    /**
     * The attributes that are mass assignable.
     *
     * @var array
     */
    protected $fillable  = [
        'image', 'email', 'password', 'full_name',
        'phone_number', 'status', 'last_login', 'is_deactivated',
        'is_verified', 'due_threshold', 'initial_due_amount', 'address'
    ];


    public function getFullName()
    {
        return ucfirst( $this->full_name);
    }

    public function getPhoneNumber()
    {
        return $this->phone_number;
    }

    public function getProfilePicture()
    {
        return get_image_url('user', str_replace('public/images/user/', '', $this->image ?? 'N/A'));
    }

    public function token()
    {
        return $this->hasOne('user_verifications', 'id', 'user_id');
    }

    /**
     * Check if Logged-In User can write a review on ticket
     *
     * @param $ticket
     * @return bool
     */
    public function canWriteReviewOn($ticket)
    {
        foreach($ticket->reviews as $review){
            if($review->ticket_id == $ticket->id && $review->user_id == $this->id){
                return false;
            }
        }
        return true;
    }

    public function isPhoneNumberVerified()
    {
        return $this->phone_is_verified;
    }

    public function isEmailVerified()
    {
        return $this->is_verified;
    }

    public function isNotVerified()
    {
        return !$this->phone_is_verified && !$this->is_verified;
    }

    public function isAdmin()
    {
        return self::hasAccess(static::DEFAULT_ROLE) || self::hasAccess(RoleConst::$current[RoleConst::ROLE_ADMIN]);
    }


    public function isCustomer()
    {
        return self::hasAccess(RoleConst::$current[RoleConst::ROLE_CUSTOMER]);
    }

    public function seeOnlyYours()
    {
        return !$this->can_see_pickers;
    }


    public static function setFolderName(): string
    {
        return 'user';
    }

    public static function setImageColumn(): string
    {
        return 'image';
    }

    public function dueAmount()
    {
        return $this->hasMany(FollowUp::class, 'customer_id')->latest('updated_at');
    }

    /**
     * @return MorphMany
     */
    public function paymentHistories()
    {
        return $this->morphMany(PaymentHistory::class, 'paymentable');
    }

    /**
     * @return HasMany
     */
    public function paymentLog(): HasMany
    {
        return $this->hasMany(PaymentHistory::class, 'customer_id');
    }
}
