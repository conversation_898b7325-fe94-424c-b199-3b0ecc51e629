<?php

namespace App\Http\Controllers\Admin;

use App\Foundation\Services\BookingService;
use App\Jobs\SendBookingConfirmationEmail;
use App\Jobs\SendBookingUpdateEmail;
use App\Models\Booking;
use Illuminate\Contracts\Filesystem\FileNotFoundException;
use Illuminate\Http\Request;
use Neputer\Supports\Mixins\Image;
use Foundation\Services\RoleService;
use Neputer\Supports\BaseController;
use Foundation\Services\UserService;
use Foundation\Requests\User\UpdateRequest;
use Yajra\DataTables\DataTables;

class BookingController extends BaseController
{
    use Image;
    /**
     * @var UserService
     */
    public  $bookingService;

    public function __construct(BookingService $bookingService)
    {
        $this->bookingService = $bookingService;

    }


    public function index(Request $request)
    {
        if ($request->ajax()){
            $rows = $this->bookingService->filter($request->all());
            return DataTables::of($rows)
                ->editColumn('created_at', function ($data) {
                    return format_date_time($data->created_at);
                })
                ->addColumn('action', function ($data) {
                    $model = 'booking';
                    return view('admin.booking.partials.action', compact('data','model'))->render();
                })
                ->editColumn('order_status', function ($data) {
                    if ($data->order_status == 'Pending') {
                        $html = '<span class="badge badge-warning">' . __(ucwords($data->order_status)) . '</span>';
                    } elseif ($data->order_status == 'Approved') {
                        $html = '<span class="badge badge-success">' . __(ucwords($data->order_status)) . '</span>';
                    } else {
                        $html = '<span class="badge badge-danger">' . __(ucwords($data->order_status)) . '</span>';
                    }
                    return $html;
                })
                ->addColumn('payment_status',function ($data){
                    if ($data->status == 'pending') {
                        $html = '<span class="badge badge-warning">' . __(ucwords($data->status)) . '</span>';
                    } elseif ($data->status == 'completed') {
                        $html = '<span class="badge badge-success">' . __(ucwords($data->status)) . '</span>';
                    } else {
                        $html = '<span class="badge badge-danger">' . __(ucwords($data->status)) . '</span>';
                    }
                    return $html;
                })
                ->addColumn('booked_date', function ($data) {
                    return format_date($data->cart->start_date).' - '.format_date($data->cart->end_date);
                })
                ->editColumn('total_price', function ($data) {
                    return currency_type($data->total_price);
                })
                ->addColumn('tour_name', function ($data) {
                    return '<a href="' . route('admin.tour.show', $data->cart?->tour_id) . '">' . $data->cart?->tour->name . '</a>';
                })
                ->rawColumns(['created_at','action','order_status','tour_name','payment_status','booked_date','total_price'])
                ->make();
        }
        return view('admin.booking.index');

    }

    /**
     */
    public function update(Request $request,$bookingId)
    {
        $booking = Booking::find($bookingId);
        $booking->order_status = $request->status;
        $booking->save();

       SendBookingUpdateEmail::dispatch( $booking);

     flash('success', 'Record successfully updated.');

       return response()->json(['success' => true,'message' => 'Record successfully updated.'], 200);
    }

    public function detail($bookingId)
    {
        $data = [];
        $data['row'] = Booking::find($bookingId);

        return view('admin.booking.show', compact('data'));
    }

    public function delete($bookingId)
    {

        $row = Booking::find($bookingId);
        $cart = $row->cart;
        $cart->delete();
        $row->delete();
        session()->flash('success', 'Record successfully deleted.');
        return redirect()->route('admin.booking.index');
    }
}
