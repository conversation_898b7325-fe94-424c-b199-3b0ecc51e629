<?php

namespace App\Http\Controllers\Admin;

use App\Models\Review;
use Illuminate\Http\Request;
use App\Http\Controllers\Controller;
use Foundation\Services\ReviewService;

class ReviewController extends Controller
{

    /**
     * @var ReviewService
     */
    protected $reviewService;

    public function __construct(ReviewService $reviewService)
    {
        $this->reviewService = $reviewService;
    }

    /**
     * Display a listing of the resource.
     * @throws Exception
     */
    public function index(Request $request)
    {
        if ($request->ajax()) {
            return datatables()
                ->of($this->reviewService->filter())
                ->addColumn('tour', function ($data) {
                    $tourUrl = route('admin.tour.edit', $data->reviewable->id);
                    return "
                        <div class='d-flex align-items-center'>
                            <a href='{$tourUrl}' target='_blank' class='mr-2'>
                                {$data->reviewable->name}
                            </a>
                            <a href='{$tourUrl}' target='_blank' class='text-muted'>
                                <i class='fa fa-external-link'></i>
                            </a>
                        </div>
                    ";
                })
                ->addColumn('user', function ($data) {
                    $userUrl = route('admin.user.edit', $data->user->id);
                    return "
                        <div>
                            <div class='d-flex align-items-center'>
                                <a href='{$userUrl}' target='_blank' class='mr-2'>
                                    {$data->user->full_name}
                                </a>
                                <a href='{$userUrl}' target='_blank' class='text-muted'>
                                    <i class='fa fa-external-link'></i>
                                </a>
                            </div>
                            <small class='text-muted'>{$data->user->email}</small>
                        </div>
                    ";
                })
                ->addColumn('rating', function ($data) {
                    $stars = str_repeat('<i class="fa fa-star text-warning"></i>', $data->rating);
                    $emptyStars = str_repeat('<i class="fa fa-star-o text-muted"></i>', 5 - $data->rating);
                    return "<div class='star-rating'>{$stars}{$emptyStars} <span class='ml-1'>{$data->rating}/5</span></div>";
                })
                ->addColumn('comment', function ($data) {
                    return str_limit($data->comment, 100);
                })
                ->addColumn('is_published', function ($data) {
                    return $data->is_published ? 'Published' : 'Not Published';
                })
                ->addColumn('created_at', function ($data) {
                    return $data->created_at->format('M d, Y');
                })
                ->addColumn('action', function ($data) {
                    $viewBtn = '<a href="' . route('admin.review.show', $data->id) . '" type="button" class="btn btn-sm btn-info mr-1 mb-1">
                        <i class="fa fa-eye"></i> View
                    </a>';

                    $deleteBtn = '<form action="' . route('admin.review.destroy', $data->id) . '" method="POST" class="d-inline" onsubmit="return confirm(\'Are you sure you want to delete this review?\')">
                        ' . csrf_field() . '
                        ' . method_field('DELETE') . '
                        <button type="submit" class="btn btn-sm btn-danger">
                            <i class="fa fa-trash"></i> Delete
                        </button>
                    </form>';

                    return '<div class="d-flex">' . $viewBtn . $deleteBtn . '</div>';
                })
                ->rawColumns(['tour', 'user', 'rating', 'comment', 'status', 'action'])
                ->make(true);
        }

        return view('admin.review.index');
    }

    /**
     * Display the specified review.
     *
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function show($id)
    {
        $review = Review::with(['reviewable', 'user'])->findOrFail($id);

        return view('admin.review.show', compact('review'));
    }

    /**
     * Toggle the published status of a review
     *
     * @param Review $review
     * @return \Illuminate\Http\JsonResponse
     */
    public function togglePublish(Review $review)
    {
        $review->update([
            'is_published' => !$review->is_published
        ]);

        return redirect()->route('admin.review.show', $review->id)->with('success', 'Review status updated successfully');
    }

    /**
     * Update the specified resource in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  \App\Models\Review  $review
     * @return \Illuminate\Http\Response
     */
    public function update(Request $request, Review $review)
    {
        try {
            $review->update([
                'is_published' => $request->input('is_published', $review->is_published)
            ]);

            if ($request->ajax() || $request->wantsJson()) {
                return response()->json([
                    'success' => true,
                    'message' => 'Review updated successfully',
                    'is_published' => $review->is_published
                ]);
            }

            flash('success', 'Review updated successfully');
            return redirect()->route('admin.review.index');

        } catch (\Exception $e) {
            if ($request->ajax() || $request->wantsJson()) {
                return response()->json([
                    'success' => false,
                    'message' => 'Failed to update review: ' . $e->getMessage()
                ], 500);
            }

            flash('error', 'Failed to update review: ' . $e->getMessage());
            return redirect()->back()->withInput();
        }
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(Review $review)
    {
        $this->reviewService->delete($review);

        if (request()->ajax() || request()->wantsJson()) {
            return response()->json([
                'success' => true,
                'message' => 'Review deleted successfully.'
            ]);
        }

        flash('success', 'Review deleted successfully.');
        return redirect()->route('admin.review.index');
    }
}
