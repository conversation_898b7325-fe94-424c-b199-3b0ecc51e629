<?php

namespace App\Http\Controllers\Auth;

use App\User;
use Foundation\Lib\Role;
use Illuminate\Support\Str;
use Illuminate\Http\Request;
use App\Http\Controllers\Controller;
use Illuminate\Support\Facades\Auth;
use Laravel\Socialite\Facades\Socialite;

class GoogleController extends Controller
{
    public function redirect()
    {
        return Socialite::driver('google')->redirect();
    }

    public function callback()
    {
        try {
            $googleUser = Socialite::driver('google')->stateless()->user();

            $email = $googleUser->getEmail();
            $fullName = $googleUser->getName() ?? $googleUser->getNickname();

            if (!$email) {
                return redirect('/login')->withErrors(['error' => 'Email not provided by Google.']);
            }

            $user = User::updateOrCreate(
                ['email' => $email],
                [
                    'full_name' => $fullName,
                    'google_id' => $googleUser->getId(),
                    'image' => $googleUser->getAvatar(),
                    // 'password' => bcrypt(Str::random(24)),
                ]
            );

            // if no role assign the customer role
            if (!$user->roles()->exists()) {
                $user->assignRole((array)Role::ROLE_CUSTOMER);
            }

            Auth::login($user);

            return redirect()->intended('/');
        } catch (\Exception $e) {
            return redirect('/login')->withErrors(['error' => 'Something went wrong: ' . $e->getMessage()]);
        }
    }
}
