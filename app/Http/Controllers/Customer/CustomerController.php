<?php

namespace App\Http\Controllers\Customer;

use App\Models\Booking;
use Illuminate\Http\Request;
use App\Rules\IntPhoneNumber;
use Illuminate\Validation\Rule;
use App\Http\Controllers\Controller;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Hash;

class CustomerController extends Controller
{

    public function dashboard()
    {
        $user = Auth::user();

        return view('frontend.customer.dashboard', compact('user'));
    }

    public function updateProfile(Request $request)
    {
        $validated = $request->validate([
            'full_name' => 'required|string',
            'email' => ['required', 'email', Rule::when($request->email != Auth::user()->email, 'unique:users,email')],
            'phone_number' => ['required', new IntPhoneNumber],
            'address' => 'required|string',
        ]);

        Auth::user()->update($validated);

        session()->flash('success', 'Profile updated successfully');

        return to_route('customer.dashboard');
    }

    public function bookings()
    {
        $user = Auth::user();

        $bookings = Booking::whereUserId($user->id)->get();

        return view('frontend.customer.booking', compact('bookings', 'user'));
    }

    public function changePassword()
    {
        $user = Auth::user();

        return view('frontend.customer.reset', compact('user'));
    }

    public function updatePassword(Request $request)
    {
        $request->validate([
            'old_password' => 'required|current_password',
            'password' => 'required|string|min:8|confirmed',
        ], [
            'old_password.current_password' => 'The old password was incorrect.',
        ]);

        $user = Auth::user();
        $user->password = Hash::make($request->password);
        $user->save();

        session()->flash('success', 'Password updated successfully');

        return to_route('customer.change-password');
    }
}
