<?php /** @noinspection SqlNoDataSourceInspection */

namespace Foundation\Services;

use App\Models\Review;
use Foundation\Models\Role;
use Neputer\Config\Status;
use Neputer\Supports\BaseService;

/**
 * Class RoleService
 * @package Foundation\Services
 */
class ReviewService extends BaseService
{
    /**
     * The Review instance
     *
     * @var Review
     */
    protected $model;

    /**
     * ReviewService constructor.
     * @param Review $review
     */
    public function __construct(Review $review)
    {
        $this->model = $review;
    }

    /**
     * Filter
     *
     * @return mixed
     */
    public function filter()
    {
        return $this->model->query()
            ->with([
                'reviewable',
                'user' => function($query) {
                    $query->select('id', 'full_name', 'email');
                }
            ])
            ->select([
                'reviews.*',
                'reviewable_type',
                'reviewable_id',
                'user_id',
                'rating',
                'comment',
                'is_published',
                'created_at',
                'updated_at',
            ])
            ->latest()
            ->get()
            ->map(function($review) {
                $review->tour_name = $review->reviewable->name ?? 'N/A';
                $review->user_name = $review->user->name ?? 'N/A';
                $review->user_email = $review->user->email ?? 'N/A';
                return $review;
            });
    }

    /**
     * Delete a review
     *
     * @param mixed $model
     * @return bool|null
     */
    public function delete($model)
    {
        if ($model instanceof Review) {
            return $model->delete();
        }
        return parent::delete($model);
    }
    public function pluck()
    {
        return $this->model->where('status', Status::ACTIVE_STATUS)->pluck('category_title', 'id');
    }
}
