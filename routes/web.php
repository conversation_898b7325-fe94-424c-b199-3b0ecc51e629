<?php

use Illuminate\Http\Request;
use Illuminate\Support\Facades\Route;
use App\Http\Controllers\HomeController;
use App\Http\Controllers\RazorPayController;
use App\Http\Controllers\Auth\GoogleController;
use Illuminate\Foundation\Auth\EmailVerificationRequest;

// Auth Routes start
Route::get('register', 'Auth\RegisterController@showRegistrationForm')->name('register');
Route::post('register', 'Auth\RegisterController@register')->name('register.submit');

Route::get('authorize', 'Auth\LoginController@showLoginForm')->name('authorize');
Route::post('authorize', 'Auth\LoginController@login');

Route::post('logout', 'Auth\LoginController@logout')->name('logout');
Route::get('force-logout', 'Auth\LoginController@logout');

Route::get('login', 'Auth\LoginController@showCustomerLoginForm')->name('login');
Route::post('login', 'Auth\LoginController@login')->name('login.submit');

Route::get('admin/login', 'Auth\LoginController@showLoginForm')->name('admin-login');

Route::middleware('throttle:6,1')->group(function () {
    Route::post('password/email', 'Auth\ForgotPasswordController@sendResetLinkEmail')->name('password.email');
    Route::get('password/reset', 'Auth\ForgotPasswordController@showLinkRequestForm')->name('password.request');
    Route::post('password/reset', 'Auth\ResetPasswordController@reset')->name('password.update');
    Route::get('password/reset/{token}', 'Auth\ResetPasswordController@showResetForm')->name('password.reset');
});

# Email Verification
Route::middleware('auth')->group(function () {
    Route::get('/email/verify', function () {
        if (auth()->user()?->hasVerifiedEmail()) {
            return redirect()->route('customer.dashboard');
        }
        return view('auth.verify-email');
    })->name('verification.notice');

    Route::get('/email/verify/{id}/{hash}', function (EmailVerificationRequest $request) {
        $request->fulfill();

        return redirect()->route('customer.dashboard');
    })->middleware('signed')->name('verification.verify');

    Route::post('/email/verification-notification', function (Request $request) {
        $request?->user()->sendEmailVerificationNotification();

        return back()->with('resent', true);
    })->middleware('throttle:6,1')->name('verification.send');
});

# Google Login
Route::get('auth/google/redirect', [GoogleController::class, 'redirect'])->name('google.redirect');
Route::get('auth/google/callback', [GoogleController::class, 'callback']);

//Auth::routes();
// Auth Routes end


Route::post('change-currency', 'HomeController@changeCurrency')->name('change-currency');

Route::get('inquiry', 'HomeController@inquiry')->name('inquiry');
Route::post('inquiry', 'HomeController@inquiryPost')->name('inquiry.post');

Route::get('/', 'HomeController@index')->name('home');
Route::get('/blog', 'HomeController@blogs')->name('blogs');
Route::get('/popular-tours', 'HomeController@popularTours')->name('popular-tours');
Route::get('/tours/{category?}', 'HomeController@tours')->name('tours');
Route::post('/tour/booking', 'HomeController@bookingTour')->name('tour.booking');
Route::get('/blogpost/{slug}', 'HomeController@blogDetail')->name('blog.detail');
Route::get('/contact-us', 'HomeController@contactUs')->name('contact-us');
Route::post('/contact-us', 'HomeController@contactUsPost')->name('contact-us.post');
Route::post('/ask-question', 'HomeController@postAskedQuestion')->name('ask-question');
Route::get('/tour/{slug}', 'HomeController@tourDetail')->name('tour.detail');
Route::get('/tour/{tour_id}/reviews', 'HomeController@loadMoreReviews')->name('tour.load-more-reviews');
Route::get('/tour/{slug}/new-detail-page', 'HomeController@tourDetailNew')->name('tour.detail-new');

Route::get('/ways-to-go/{slug}', 'HomeController@waysToGo')->name('ways-to-go');

Route::get('/cart', 'HomeController@cartDetail')->name('carts');
Route::middleware('auth')->group(function () {
    Route::get('/checkout', 'HomeController@checkout')->name('checkout');
    Route::post('/pay-via-stripe', 'PaymentController@payViaStripe')->name('pay-via-stripe');
    Route::get('/payment/success', 'PaymentController@paymentSuccess')->name('payment.success');
});

Route::post('stripe/checkout', 'PaymentController@stripeCheckout')->name('stripe.checkout');
Route::get('stripe/checkout/success', 'PaymentController@stripeCheckoutSuccess')->name('stripe.checkout.success');

Route::post('razorpay-payment', [RazorPayController::class, 'payViaRazorPay'])->name('razorpay.payment');



/*Route::get('/operator', 'HomeController@operator')->name('operator');
Route::get('/dashboard', 'HomeController@dashboard')->name('dashboard');
Route::get('/booking', 'HomeController@booking')->name('booking');
Route::get('/bookingdetails', 'HomeController@bookingdetails')->name('bookingdetails');
Route::get('/resetpassword', 'HomeController@resetpassword')->name('resetpassword');*/



Route::group(['prefix' => 'laravel-filemanager', 'middleware' => ['web', 'auth']], function () {
    \UniSharp\LaravelFilemanager\Lfm::routes();
});

Route::get('/page/{slug}', 'HomeController@pages')->name('page.detail');

Route::get('sitemap.xml', [HomeController::class, 'sitemap'])->name('sitemap');
