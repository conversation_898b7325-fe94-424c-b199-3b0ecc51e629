@extends('frontend.layouts.master')
@push('css')
<link href="{{ asset('assets/css/blog.css') }}" rel="stylesheet">
@endpush

@push('title', 'Change Password')

@section('content')
<main>

    <div class="container container-custom dashboard-section margin_60_35">
        <div class="row">
            @include('frontend.customer.user-dashboard', ['user' => $user])

            <div class="col-lg-8">
                @include('common.alert')
                <div class="contact_wraper">
                    <div class="contact-info">
                        <h4 class="title">Change Password</h4>
                        <p class="desc">Change your profile password</p>
                        <div class="contact_form">
                            {{ Form::model($user, ['route' => 'customer.update-password', 'method' => 'patch']) }}
                                <div class="row">
                                    <div class="col-md-4">
                                        <div class="input_group">
                                            <label for="old_password" class="form-label">Old Password <span
                                                    class="text-red-800">*</span></label>
                                            {{ Form::password('old_password', ['class' => 'form-control form-control--sm br-4'.($errors->has('old_password') ? ' is-invalid' : ''), 'id' => 'old_password', 'placeholder' => 'Your old password...', 'required']) }}
                                            @error('old_password')
                                            <span class="invalid-feedback" role="alert">
                                                <strong>{{ $message }}</strong>
                                            </span>
                                            @enderror
                                        </div>
                                    </div>
                                    <div class="col-md-4">
                                        <div class="input_group">
                                            <label for="password" class="form-label">New Password <span
                                                    class="text-red-800">*</span></label>
                                            {{ Form::password('password', ['class' => 'form-control form-control--sm br-4'.($errors->has('password') ? ' is-invalid' : ''), 'id' => 'password', 'placeholder' => 'Your new password...', 'required']) }}
                                            @error('password')
                                            <span class="invalid-feedback" role="alert">
                                                <strong>{{ $message }}</strong>
                                            </span>
                                            @enderror
                                        </div>
                                    </div>
                                    <div class="col-md-4">
                                        <div class="input_group">
                                            <label for="password_confirmation" class="form-label">Confirm Password <span
                                                    class="text-red-800">*</span></label>
                                            {{ Form::password('password_confirmation', ['class' => 'form-control form-control--sm br-4'.($errors->has('password_confirmation') ? ' is-invalid' : ''), 'id' => 'password_confirmation', 'placeholder' => 'Your confirm password...', 'required']) }}
                                            @error('password_confirmation')
                                            <span class="invalid-feedback" role="alert">
                                                <strong>{{ $message }}</strong>
                                            </span>
                                            @enderror
                                        </div>
                                    </div>
                                </div>
                                <button class="update-btn" type="submit">
                                    Update
                                </button>
                            {{ Form::close() }}
                        </div>
                    </div>
                </div>
            </div>
        </div>

    </div>
</main>

@endsection
