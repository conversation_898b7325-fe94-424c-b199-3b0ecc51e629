@extends('frontend.layouts.master')

@push('title', 'Edit Review')

@section('content')
    <main>
        <div class="container container-custom dashboard-section margin_60_35">
            <div class="row">
                @include('frontend.customer.user-dashboard', ['user' => $user])

                <div class="col-lg-8">
                    <div class="contact_wraper">
                        <div class="contact-info">
                            <div class="d-flex justify-content-between align-items-center mb-4">
                                <h4 class="title mb-0">Edit Review</h4>
                                <a href="{{ route('customer.reviews.show', $review->id) }}" class="btn btn-sm btn-outline-secondary">
                                    <i class="fa fa-arrow-left"></i> Back to Review
                                </a>
                            </div>

                            @include('common.errors')

                            <div class="card">
                                <div class="card-body">
                                    <h5 class="card-title">{{ $review->reviewable->name }}</h5>

                                    <form action="{{ route('customer.reviews.update', $review->id) }}" method="POST">
                                        @csrf
                                        @method('PUT')

                                        <div class="form-group mb-4">
                                            <label for="rating" class="form-label">Rating</label>
                                            <select class="form-control @error('rating') is-invalid @enderror"
                                                    id="rating"
                                                    name="rating"
                                                    required>
                                                @for($i = 5; $i >= 1; $i--)
                                                    <option value="{{ $i }}" {{ old('rating', $review->rating) == $i ? 'selected' : '' }}>
                                                        {{ $i }} - {{ $i === 1 ? 'Poor' : ($i === 2 ? 'Fair' : ($i === 3 ? 'Good' : ($i === 4 ? 'Very Good' : 'Excellent'))) }}
                                                    </option>
                                                @endfor
                                            </select>
                                            @error('rating')
                                                <div class="invalid-feedback">{{ $message }}</div>
                                            @enderror
                                        </div>

                                        <div class="form-group mb-4">
                                            <label for="comment" class="form-label">Your Review</label>
                                            <textarea class="form-control @error('comment') is-invalid @enderror"
                                                    id="comment"
                                                    name="comment"
                                                    rows="5"
                                                    required>{{ old('comment', $review->comment) }}</textarea>
                                            @error('comment')
                                                <div class="invalid-feedback">{{ $message }}</div>
                                            @enderror
                                        </div>

                                        <div class="d-flex justify-content-between">
                                            <button type="submit" class="btn btn-primary">
                                                <i class="fa fa-save"></i> Update Review
                                            </button>

                                            <a href="{{ route('customer.reviews.show', $review->id) }}" class="btn btn-outline-secondary">
                                                Cancel
                                            </a>
                                        </div>
                                    </form>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </main>
@endsection
