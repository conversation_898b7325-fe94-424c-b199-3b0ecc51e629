<div class="header__mainContainer">
    @if(auth()->check() && auth()->user()->hasRole('customer') && !auth()->user()->email_verified_at)
        <div class="email-verify">
            Please verify your email address. <a href="{{ route('verification.notice') }}">Click here</a>
        </div>
    @endif

    <div class="header main-header home-header">
        <div class="container container-custom">

            <div class="header-bottom--wrapper">
                <a href="{{ route('home') }}" class="header-bottom__logo-wrapper">
                    <img src="{{  get_image_url('setting',config_value_by_key('logo'))}}" alt="logoimg" class="header-bottom__logo-image" width="150" height="36">
                </a>
                <ul class="nav-menu">
                    <li class="nav-menu__item megamenu_togglebutton">
                        <a href="#" class="nav-menu__link dropdown-toggles" type="button" id="dropdownMenuButton " href="javascript:void(0)" role="button">
                            Ways To Go

                            <span class="icon-wrapper">
                                <iconify-icon icon="ri:arrow-down-s-line" class="arrow-icon"></iconify-icon>
                            </span>
                        </a>
                        <div class="dropdownmenusection">
                            <div class="dropdownmenusection__container">
                                <div class="nav flex-column nav-pills navmenusection" id="v-pills-tab" role="tablist" aria-orientation="vertical">
                                    @foreach($tourCategory as $category)
                                    <a class="nav-link {{ $loop->first ? 'active' : ''  }} tab_menu-btn" id="v-pills-{{ $category->slug }}-tab" data-toggle="pill" href="#v-pills-{{ $category->slug }}" role="tab" aria-controls="v-pills-{{ $category->slug }}" aria-selected="true">{{ $category->category_title }}</a>
                                    @endforeach
                                </div>
                                <div class="tab-content navmenucontent" id="v-pills-tabContent">
                                    @foreach($tourCategory as $category)
                                    <div class="tab-pane fade show {{ $loop->first ? 'active' : ''  }}" id="v-pills-{{ $category->slug }}" role="tabpanel" aria-labelledby="v-pills-{{ $category->slug }}-tab">
                                        <div class="headingwrapper">
                                            <a href="{{ route('tours', $category->slug) }}" class="megamenu__link-btn">{{ $category->category_title }}
                                                <iconify-icon icon="akar-icons:arrow-right" class="arrow-icon"></iconify-icon>
                                            </a>

                                            <button class="close__megamenu--btn">
                                                <iconify-icon icon="gridicons:cross"></iconify-icon>
                                            </button>
                                        </div>

                                        <div class="submenu-block__rowwrapper">

                                            <ul class="submenu-listwrappers">
                                                @foreach($category->tour()->whereStatus(1)->get() as $tour)
                                                <li class="list-items">
                                                    <a href="{{ route('tour.detail', $tour->slug) }}" class="list-items__link">
                                                        {{ $tour->name }}
                                                    </a>
                                                </li>
                                                @endforeach
                                            </ul>

                                        </div>
                                    </div>
                                    @endforeach
                                </div>
                            </div>
                        </div>
                    </li>


                    <li class="nav-menu__item">
                        <a href="{{ route('blogs') }}" class="nav-menu__link">Blogs</a>
                    </li>
                    <li class="nav-menu__item">
                        <a href="{{ route('page.detail','about') }}" class="nav-menu__link ">Who We Are</a>
                    </li>
                    <li class="nav-menu__item">
                        <a href="{{ route('contact-us') }}" class="nav-menu__link">Contact Us</a>
                    </li>
                    @if(auth()->check())
                    <li class="nav-menu__item">
                        <a href="{{ auth()->user()->hasRole('customer') ? route('customer.dashboard') : route('admin.dashboard.index') }}" class="nav-menu__link">Dashboard</a>
                    </li>
                    <li class="nav-menu__item">
                        <a href="javascript:void(0);" onclick="event.preventDefault();document.getElementById('logout-form').submit();" class="nav-menu__link"><i class="icon-logout"></i>
                            Log Out</a>
                    </li>
                    <form id="logout-form" action="{{ route('logout') }}" method="POST" style="display: none;">
                        {!! csrf_field() !!}
                    </form>
                    @else
                    <li class="nav-menu__item">
                        <a href="#sign-in-dialog" id="sign-in" class="logins-btn" title="Sign In">Sign In</a>
                    </li>
                    @endif
                    <div class="dropdown">
                        <button class="btn dropdown-toggle" type="button" id="dropdownMenuButton" data-toggle="dropdown" aria-haspopup="true" aria-expanded="false">
                            {{ session()->get('currency')  }}
                        </button>
                        <div class="dropdown-menu" aria-labelledby="dropdownMenuButton">
                            <a class="dropdown-item" style="cursor: pointer" onclick="handleChangeCurrency('INR')">INR</a>
                            <a class="dropdown-item" style="cursor: pointer" onclick="handleChangeCurrency('USD')">USD</a>
                        </div>
                    </div>
                </ul>

            </div>
        </div>
    </div>
</div>
<div id="megamenuoverlay"></div>


<div class="header--sm homeheader-sm">
    <div class="container container-custom">
        <div class="header--wrapper">
            <a href="{{ route('home') }}" class="header__logo-wrapper">
                <img src="{{  get_image_url('setting',config_value_by_key('logo')) }}" alt="logoimg" class="header__logo-image">
            </a>
            <div class="menu-toggle">
                <a href="" class="menu-bar">
                    <iconify-icon icon="octicon:three-bars-16"></iconify-icon>
                </a>

                <div class="offcanvas offcanvas-end" id="menuoffcanvasExample" tabindex="-1" aria-modal="true" role="dialog">
                    <div class="offcanvas-header">
                        <button type="button" class="btn-closes" data-bs-dismiss="offcanvas">
                            <iconify-icon icon="akar-icons:cross" class="close-icon"></iconify-icon>
                        </button>
                    </div>
                    <div class="offcanvas-body">
                        <ul class="menu-list">
                            <li><a href="{{ route('home') }}" class="menu-list-item">Home</a></li>

                            <li>
                                <a href="javascript:void(0)" class="menu-list-item has-menu">Ways to Go </a>
                                <ul class="submenu">
                                    <div class="submenu-header">
                                        <div class="back-wrapper">
                                            <button class="back-btn"><iconify-icon icon="solar:arrow-left-outline"></iconify-icon>Back</button>
                                        </div>
                                        <button type="button" class="btn-closes">
                                            <iconify-icon icon="akar-icons:cross" class="close-icon"></iconify-icon></button>
                                    </div>

                                    @foreach($tourCategory as $category)
                                    <li>
                                        <a href="javascript:void(0)" class="submenu-item has-menu">{{ $category->category_title }}</a>
                                        <ul class="sub-submenu">
                                            <div class="submenu-header">
                                                <div class="back-wrapper">
                                                    <button class="back-btn"><iconify-icon icon="solar:arrow-left-outline"></iconify-icon>Back</button>
                                                </div>
                                                <button type="button" class="btn-closes">
                                                    <iconify-icon icon="akar-icons:cross" class="close-icon"></iconify-icon></button>
                                            </div>
                                            <h4 class="menu--title"><a href="{{ route('tours', $category->slug) }}">{{ $category->category_title }}</a></h4>

                                            @foreach($category->tour()->whereStatus(1)->get() as $tour)
                                            <li>
                                                <a href="{{ route('tour.detail', $tour->slug) }}">{{ $tour->name }}</a>
                                            </li>
                                            @endforeach
                                        </ul>
                                    </li>
                                    @endforeach
                                </ul>
                            </li>

                            {{--<li>
                                <a href="javascript:void(0)" class="menu-list-item has-menu">How To Travel </a>
                                <ul class="submenu">
                                    <div class="submenu-header">
                                        <div class="back-wrapper">
                                            <button class="back-btn"><iconify-icon icon="solar:arrow-left-outline"></iconify-icon>Back</button>
                                        </div>
                                        <button type="button" class="btn-closes">
                                            <iconify-icon icon="akar-icons:cross" class="close-icon"></iconify-icon></button>
                                    </div>

                                    <li>
                                        <a href="javascript:void(0)" class="submenu-item">Schedule Travel</a>
                                    </li>

                                    <li>
                                        <a href="javascript:void(0)" class="submenu-item">Bespoke Travel</a>
                                    </li>
                                </ul>
                            </li>--}}

                            <li>
                                <a href="{{ route('blogs') }}" class="menu-list-item">Blogs</a>
                            </li>


                            <li>
                                <a href="{{ route('page.detail', 'about') }}" class="menu-list-item">Who We Are</a>
                            </li>


                            <li>
                                <a href="{{ route('contact-us') }}" class="menu-list-item">Contact Us</a>
                            </li>

                            @if(auth()->check())
                                <li>
                                    <a href="{{ auth()->user()->hasRole('customer') ? route('customer.dashboard') : route('admin.dashboard.index') }}" class="menu-list-item">Dashboard</a>
                                </li>

                                <li>
                                    <a href="javascript:void(0);" onclick="event.preventDefault();document.getElementById('logout-form').submit();" class="menu-list-item">Log Out</a>
                                </li>
                            @else
                                <li>
                                    <a href="{{ route('login') }}" class="menu-list-item">Sign In</a>
                                </li>
                            @endif

                            <li>
                                <span class="dropdown currencyDropdown">
                                <button class="btn dropdown-toggle" type="button" id="dropdownMenuButton" data-toggle="dropdown" aria-haspopup="true" aria-expanded="false">
                                    {{ session()->get('currency')  }}
                                </button>
                                <div class="dropdown-menu" aria-labelledby="dropdownMenuButton">
                                    <a class="dropdown-item" style="cursor: pointer" onclick="handleChangeCurrency('INR')">INR</a>
                                    <a class="dropdown-item" style="cursor: pointer" onclick="handleChangeCurrency('USD')">USD</a>
                                </div>
                            </span>
                            </li>

                        </ul>
                    </div>
                </div>
            </div>
        </div>
    </div>


</div>
