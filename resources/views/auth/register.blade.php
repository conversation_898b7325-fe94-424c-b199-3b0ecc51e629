@extends('auth.auth_layout')

@push('title', 'Register')

@section('content')
    <div id="login">
        <aside>
            <figure>
                <a href="{{ route('home') }}"><img src="{{ get_image_url('setting', config_value_by_key('logo')) }}"
                        width="150" height="70" data-retina="true" alt="" class="logo_sticky"></a>
            </figure>
            @include('common.errors')
            <form autocomplete="off" action="{{ route('register.submit') }}" method="post" id="registerForm">
                @csrf
                <div class="form-group">
                    <label>Full Name</label>
                    <input class="form-control" type="text" name="full_name" value="{{ old('full_name') }}" required>
                    <i class="ti-user"></i>
                    @error('full_name')
                        <span class="text-danger">{{ $message }}</span>
                    @enderror
                </div>

                <div class="form-group">
                    <label>Your Email</label>
                    <input class="form-control" type="email" name="email" value="{{ old('email') }}" required>
                    <i class="icon_mail_alt"></i>
                    @error('email')
                        <span class="text-danger">{{ $message }}</span>
                    @enderror
                </div>
                <div class="form-group">
                    <label>Your password</label>
                    <input class="form-control" type="password" id="password1" name="password" required>
                    <i class="icon_lock_alt"></i>
                    @error('password')
                        <span class="text-danger">{{ $message }}</span>
                    @enderror
                </div>
                <div class="form-group">
                    <label>Confirm password</label>
                    <input class="form-control" type="password" id="password2" name="password_confirmation" required>
                    <i class="icon_lock_alt"></i>
                </div>
                <div id="pass-info" class="clearfix"></div>

                @error('recaptcha_token')
                    <p class="text-danger">{{ $message }}</p>
                @enderror

                <button type="submit" class="btn_1 rounded full-width add_top_30">
                    Register to Orrog
                </button>

                <div class="login-separator text-center mb-2"><span>or</span></div>
                <a href="{{ route('google.redirect') }}" class="btn_1 rounded full-width google-login-btn">
                    <img src="{{ asset('images/google-logo.png') }}" alt="Google" width="18" height="18">
                    Continue with Google
                </a>

                <div class="text-center add_top_10">
                    Already have an account? <strong><a href="{{ route('login') }}">Sign In</a></strong>
                </div>
            </form>
            <div class="copy">© {{ date('Y') }} Orrog</div>
        </aside>
    </div>
@endsection

@push('js')
    <script src="{{ asset('assets/js/jquery.validate.min.js') }}"></script>

    <!-- Add reCAPTCHA script -->
    <script src="https://www.google.com/recaptcha/api.js?render={{ config('services.recaptcha.site_key') }}"></script>
    <script src="{{ asset('assets/js/recaptcha-validation.js') }}"></script>

    <script>
        $(document).ready(function() {
            // Initialize reCAPTCHA
            recaptchaHandler.init('{{ config('services.recaptcha.site_key') }}');

            $('#registerForm').validate({
                errorClass: 'text-danger',
                rules: {
                    full_name: {
                        required: true,
                    },
                    email: {
                        required: true,
                        email: true
                    },
                    password: {
                        required: true,
                        minlength: 6
                    },
                    password_confirmation: {
                        required: true,
                        equalTo: '#password1'
                    }
                },
                submitHandler: function(form) {
                    recaptchaHandler.getToken()
                        .then(function(token) {
                            recaptchaHandler.injectToken(form, token);
                            form.submit();
                        })
                        .catch(function(err) {
                            alert("reCAPTCHA failed: " + err);
                        });
                }
            });
        })
    </script>
@endpush
