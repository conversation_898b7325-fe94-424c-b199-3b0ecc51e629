@extends('admin.layouts.master')

@push('css')
    <link href="{{ asset('dist/css/plugin.css') }}" rel="stylesheet">
    <link href="{{ asset('dist/css/list.css') }}" rel="stylesheet">
@endpush

@section('title', 'Booking List')

@section('content')
    @include('admin.common.breadcrumbs', [
        'title'=> 'Report',
        'panel'=> 'booking',
    ])
    <div class="wrapper wrapper-content animated fadeInRight">
        @include('admin.common.success_error_message')

        <div class="row">
            <div class="col-lg-12">
                <div class="ibox ">
                    <div class="ibox-title ibox-title-border">
                        <h5>Booking List</h5>
                    </div>
                    <div class="ibox-content ibox-content-custom">
                        @include('admin.booking.partials.table')

                    </div>
                </div>
            </div>
        </div>
    </div>
@endsection

@push('js')
    <script src="{{ asset('dist/js/bulk-action-manager.min.js') }}"></script>

    <script>
        var DataTableOptions = {
            defaultPagination: {{ \Foundation\Lib\Meta::get('default_system_pagination', 25) }},
            export: {
                columns: [ 0, 1, 2, 3, 4],
                title: 'Application : Booking Print'
            },
            ajax: {
                url: '{{ route('admin.booking.index') }}',
                dataType: 'json',
                type: 'GET',
                data: function (data) {
                    data._token = '{{ csrf_token() }}'
                }
            },
            columns: [
                { data: 'tour_name', orderable: 'tour_name' },
                { data: 'payment_status', orderable: true },
                { data: 'order_status', orderable: true },
                { data: 'total_price', orderable: true },
                { data: 'booked_date', orderable: true },
                { data: 'created_at', orderable: true },
                { data: 'action', orderable: false },
            ],
            filters: function () {
                this.api().columns([0]).every(function () {
                    var column = this;
                    $('<input class="form-control">')
                        .appendTo($(column.footer()).empty())
                        .on('keyup change', function () {
                            column.search($(this).val(), false, false, true).draw();
                        });
                });
                this.api().columns([1]).every(function () {
                    var column = this;
                    $('<input class="form-control">')
                        .appendTo($(column.footer()).empty())
                        .on('keyup change', function () {
                            column.search($(this).val(), false, false, true).draw();
                        });
                });
                this.api().columns([2]).every(function () {
                    var column = this;
                    $('<input class="form-control">')
                        .appendTo($(column.footer()).empty())
                        .on('keyup change', function () {
                            column.search($(this).val(), false, false, true).draw();
                        });
                });
                this.api().columns([3]).every(function () {
                    var column = this;
                    $('<input class="form-control">')
                        .appendTo($(column.footer()).empty())
                        .on('keyup change', function () {
                            column.search($(this).val(), false, false, true).draw();
                        });
                });
            }
        };
    </script>
    <script src="{{ asset('dist/js/plugin.js') }}"></script>
    <script src="{{ asset('dist/js/list.js') }}"></script>
    @include('admin.common.summary-script', ['table' => 'roles'])
@endpush
