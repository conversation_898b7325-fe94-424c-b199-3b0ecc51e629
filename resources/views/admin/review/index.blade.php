@extends('admin.layouts.master')

@push('css')
    <link href="{{ asset('dist/css/plugin.css') }}" rel="stylesheet">
    <link href="{{ asset('dist/css/list.css') }}" rel="stylesheet">
@endpush

@section('title', 'Review List')

@section('content')
    @include('admin.common.breadcrumbs', [
        'title' => 'List',
        'panel' => 'Review',
    ])
    <div class="wrapper wrapper-content animated fadeInRight">
        @include('admin.common.success_error_message')

        <div class="row">
            <div class="col-lg-12">
                <div class="ibox ">
                    <div class="ibox-title ibox-title-border">
                        <h5>Review List</h5>
                    </div>
                    <div class="ibox-content ibox-content-custom">
                        @include('admin.review.partials.table')
                    </div>
                </div>
            </div>
        </div>
    </div>
@endsection

@push('js')
    <script src="{{ asset('dist/js/bulk-action-manager.min.js') }}"></script>

    <script>
        var DataTableOptions = {
            defaultPagination: {{ \Foundation\Lib\Meta::get('default_system_pagination', 25) }},
            export: {
                columns: [0, 1, 2, 3, 4],
                title: 'Application : Review Print'
            },
            ajax: {
                url: '{{ route('admin.review.index') }}',
                dataType: 'json',
                type: 'GET',
                data: function(data) {
                    data._token = '{{ csrf_token() }}'
                }
            },
            columns: [{
                    data: 'tour',
                    orderable: true
                },
                {
                    data: 'user',
                    orderable: true
                },
                {
                    data: 'rating',
                    orderable: true
                },
                {
                    data: 'comment',
                    orderable: true
                },
                {
                    data: 'is_published',
                    orderable: true
                },
                {
                    data: 'created_at',
                    orderable: true
                },
                {
                    data: 'action',
                    orderable: false
                },
            ],
            filters: function() {
                this.api().columns([0]).every(function() {
                    var column = this;
                    $('<input class="form-control">')
                        .appendTo($(column.footer()).empty())
                        .on('keyup change', function() {
                            column.search($(this).val(), false, false, true).draw();
                        });
                });
                this.api().columns([1]).every(function() {
                    var column = this;
                    $('<input class="form-control">')
                        .appendTo($(column.footer()).empty())
                        .on('keyup change', function() {
                            column.search($(this).val(), false, false, true).draw();
                        });
                });
                this.api().columns([2]).every(function() {
                    var column = this;
                    $('<input class="form-control">')
                        .appendTo($(column.footer()).empty())
                        .on('keyup change', function() {
                            column.search($(this).val(), false, false, true).draw();
                        });
                });
                this.api().columns([3]).every(function() {
                    var column = this;
                    $('<input class="form-control">')
                        .appendTo($(column.footer()).empty())
                        .on('keyup change', function() {
                            column.search($(this).val(), false, false, true).draw();
                        });
                });
            }
        };
    </script>
    <script src="{{ asset('dist/js/plugin.js') }}"></script>
    <script src="{{ asset('dist/js/list.js') }}"></script>
    @include('admin.common.summary-script', ['table' => 'roles'])
@endpush
